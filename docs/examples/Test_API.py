import requests, base64, os, sys,json

def test_crawl_endpoint(crawl_payload):

    response = requests.post("http://localhost:11235/crawl", json=crawl_payload)
    data = response.json()
    print(f"Crawl Response: {json.dumps(data, indent=2)}")
    return        


# dict_keys(['url', 'html', 'success', 'cleaned_html', 'media', 'links', 'downloaded_files', 'js_execution_result', 'screenshot', 'pdf', 'mhtml', 'extracted_content', 'metadata', 'error_message', 'session_id', 'response_headers', 'status_code', 'ssl_certificate', 'dispatch_result', 'redirected_url', 'network_requests', 'console_messages', 'tables', 'markdown'])


if __name__ == "__main__":
    os.system('cls' if os.name == 'nt' else 'clear')


    My_LLMConfig= {
            "provider": "ollama/smollm2",
            "base_url": "http://localhost:11434",
            "api_key": "sk1_asdf",
            "instruction": """Translate them in French.""",
        }
    

    My_Schema= {
                "type": "object",
                "properties": {
                    "title": {
                        "type": "string"
                        },
                    "content": {
                        "type": "string"
                        },
                    "links": {
                        "type": "array",
                        "items": {
                            "type": "string"
                            }
                        }
                    }, 
                "required": ["title", "content", "links"]
                }
    
     
    crawl_payload = {
        "urls": ["https://example.com"],
        "browser_config": {
            "headless": True,
            "viewport": {
                "width": 1200
            }
        },
        "crawler_config": {
            "type": "CrawlerRunConfig",
            "params": {
                "scraping_strategy": {
                    "type": "WebScrapingStrategy",
                    "params": {}
                },
                "stream": False,
                "screenshot": False,
                "cache_mode": "bypass",
                "extraction_strategy": {
                    "type": "LLMExtractionStrategy",
                    "params": {
                        "llmconfig": My_LLMConfig,
                        "api_key": My_LLMConfig["api_key"],
                        "instruction": """Translate them in French.""",
                        "schema": My_Schema,
                        "extraction_type": "block",
                        "chunk_token_threshold": 4000,
                        "overlap_rate": 0.1,
                        "word_token_rate": 0.75,
                        "apply_chunking": True,
                        "extra_args": {},
                        "verbose": False
                    }
                },
                "verbose": True,
                "check_robots_txt": False,
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.6367.207 Safari/537.36",
                "exclude_external_links": True,
                "exclude_social_media_domains": [
                            "facebook.com",
                            "twitter.com",
                            "x.com",
                            "linkedin.com",
                            "instagram.com",
                            "pinterest.com",
                            "tiktok.com",
                            "snapchat.com",
                            "reddit.com"
                        ],
            }
            
        }
    }

    schema = {
            "type": "object",
            "properties": {
                "article_title": {
                    "type": "string",
                    "description": "The main title of the news article",
                },
                "summary": {
                    "type": "string",
                    "description": "A brief summary of the article content",
                },
                "main_topics": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Main topics or themes discussed in the article",
                },
            },
        }


    crawl_payload = {
            "urls": ["https://www.iana.org/help/example-domains"],
            "priority": 8,
            "extraction_config": {
                "type": "llm",
                "params": {
                    "provider": "openai/parm-v2-qwq-qwen-2.5-o1-3b",
                    "base_url": "http://localhost:1234",
                    "schema": schema,
                    "extraction_type": "schema",
                    "instruction": "Extract the main article information including title, summary, and main topics.",
                },
            },
            "extra": {"word_count_threshold": 1},
            "crawler_params": {"verbose": True},
        }





    print("\n=== Testing Cosine Extraction ===")
    crawl_payload1 = {
        "urls": ["https://www.iana.org/help/example-domains"],
        "priority": 8,
        "extraction_config": {
            "type": "cosine",
            "params": {
                "semantic_filter": "business finance economy",
                "word_count_threshold": 10,
                "max_dist": 0.2,
                "top_k": 3,
            },
        },
    }


    #playground
    crawl_payload1={
                "urls": [
                    "https://example.com"
                ],
                "crawler_config": {
                    "type": "CrawlerRunConfig",
                    "params": {
                        "scraping_strategy": {
                            "type": "WebScrapingStrategy",
                            "params": {}
                        },
                        "exclude_social_media_domains": [
                            "facebook.com",
                            "twitter.com",
                            "x.com",
                            "linkedin.com",
                            "instagram.com",
                            "pinterest.com",
                            "tiktok.com",
                            "snapchat.com",
                            "reddit.com"
                        ],
                        "stream": True
                    }
                }
            }
        


    crawl_payload1={
                "urls": [
                    "https://www.iana.org/help/example-domains"
                ],
                "crawler_config": {
                    "type": "CrawlerRunConfig",
                    "params": {
                        "word_count_threshold": 10,
                        "css_selector": "main.content",
                        "excluded_tags": [
                            "nav",
                            "footer"
                        ],
                        "scraping_strategy": {
                            "type": "WebScrapingStrategy",
                            "params": {}
                        },
                        "cache_mode": {
                            "type": "CacheMode",
                            "params": "enabled"
                        },
                        #"session_id": "persistent_session",
                        #"page_timeout": 30000,
                        #"wait_for": "css:.loaded-block",
                        #"js_code": "document.querySelector('.show-more')?.click();",
                        #"simulate_user": True,
                        #"magic": True,
                        "screenshot": True,
                        "pdf": True,
                        "exclude_social_media_domains": [
                            "facebook.com",
                            "twitter.com",
                            "x.com",
                            "linkedin.com",
                            "instagram.com",
                            "pinterest.com",
                            "tiktok.com",
                            "snapchat.com",
                            "reddit.com"
                        ],
                        "exclude_external_links": True,
                        "check_robots_txt": True
                    }
                }
            }



    test_crawl_endpoint(crawl_payload)




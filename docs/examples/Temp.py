import requests, base64, os, sys,json

crawl_payload = {
    "urls": ["https://example.com"],
    "browser_config": {
        "headless": True
        },
    "crawler_config": {
        "stream": False,
        "screenshot": False
        }
}


def test_endpoint(data):

    response = requests.post("http://localhost:11235/crawl", json=data)
    result = response.json()["results"][0]
    print(result.keys())
    print(f"url:\n {result['url']}\n")
    print(f"html:\n {result['html']}\n")
    print(f"success:\n {result['success']}\n")
    print(f"cleaned_html:\n {result['cleaned_html']}\n")
    #print(f"fit_html:\n {result['fit_html']}\n")
    print(f"media:\n {result['media']}\n")
    print(f"links:\n {result['links']}\n")
    print(f"downloaded_files:\n {result['downloaded_files']}\n")
    print(f"screenshot:\n {result['screenshot']}\n")
    print(f"pdf:\n {result['pdf']}\n")
    print(f"mhtml:\n {result['mhtml']}\n")
    print(f"markdown:\n {result['markdown']}\n")
    print(f"extracted_content:\n {result['extracted_content']}\n")
    print(f"metadata:\n {result['metadata']}\n")
    print(f"error_message:\n {result['error_message']}\n")
    print(f"session_id:\n {result['session_id']}\n")
    print(f"metadata:\n {result['metadata']}\n")
    print(f"response_headers:\n {result['response_headers']}\n")
    print(f"status_code:\n {result['status_code']}\n")
    print(f"ssl_certificate:\n {result['ssl_certificate']}\n")
    print(f"dispatch_result:\n {result['dispatch_result']}\n")

    print(f"js_execution_result:\n {result['js_execution_result']}\n")
    
    return



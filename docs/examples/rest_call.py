import requests, base64, os

data = {
    "urls": ["https://www.nbcnews.com/business"],
    "screenshot": True,
}

# Example of filtering the content using CSS selectors
data = {
    "urls": ["https://www.nbcnews.com/business"],
    "css_selector": "article",
    "screenshot": True,
}

# Example of using LLM to extract content
data = {
    "urls": ["https://www.nbcnews.com/business"],
    "extraction_strategy": "LLMExtractionStrategy",
    "extraction_strategy_args": {
        "provider": "openai/smollm2",
        "base_url": "http://localhost:1235/v1",
        "api_token": "asdf",
        "instruction": """I am interested in only financial news, 
        and translate them in French.""",
    },
}



response = requests.post("http://localhost:11235/crawl", json=data)
result = response.json()["results"][0]
print(result.keys())
print(f"markdown:\n {result['markdown']}\n")
print(f"cleaned_html:\n {result['cleaned_html']}\n")
print(f"media:\n {result['media']}\n")
print(f"links:\n {result['links']}\n")
print(f"js_execution_result:\n {result['js_execution_result']}\n")
print(f"screenshot:\n {result['screenshot']}\n")
print(f"extracted_content:\n {result['extracted_content']}\n")
print(f"metadata:\n {result['metadata']}\n")
print(f"response_headers:\n {result['response_headers']}\n")







#print(response.json())
# dict_keys(['url', 'html', 'success', 'cleaned_html', 'media',
# 'links', 'screenshot', 'markdown', 'extracted_content',
# 'metadata', 'error_message'])
if result["screenshot"]:                                                                                                                                                        
#    print ("Screeenshot")
    with open("screenshot.png", "wb") as f:
        f.write(base64.b64decode(result["screenshot"]))

if result["html"]:
    with open("page.html", "w") as f:
        f.write(result["html"])






# Example of executing a JS script on the page before extracting the content
data = {
    "urls": ["https://www.iana.org/help/example-domains"],
    "screenshot": True,
    "js": [
        """
    const loadMoreButton = Array.from(document.querySelectorAll('button')).
    find(button => button.textContent.includes('Load More'));
    loadMoreButton && loadMoreButton.click();
    """
    ],
}



# Example of using a custom extraction strategy
data = {
    "urls": ["https://www.iana.org/help/example-domains"],
    "extraction_strategy": "CosineStrategy",
    "extraction_strategy_args": {"semantic_filter": "inflation rent prices"},
}


# Example of using LLM to extract content
data = {
    "urls": ["https://www.nbcnews.com/business"],
    "extraction_strategy": "LLMExtractionStrategy",
    "extraction_strategy_args": {
        "provider": "groq/llama3-8b-8192",
        "api_token": os.environ.get("GROQ_API_KEY"),
        "instruction": """I am interested in only financial news, 
        and translate them in French.""",
    },
}
